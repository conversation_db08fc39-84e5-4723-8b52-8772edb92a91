<template>
  <div class="example-page">
    <Head
      v-model:search-value="searchText"
      v-model:selected-value="selectedValue"
      :search-placeholder="'请输入企业名称'"
      :dropdown-title="'选择类型'"
      :dropdown-options="dropdownOptions"
      @search="handleSearch"
      @clear="handleClear"
    />

    <!-- 显示当前状态 -->
    <div class="status-display">
      <div class="status-item">
        <span class="label">搜索内容：</span>
        <span class="value">{{ searchText || '暂无' }}</span>
      </div>
      <div class="status-item">
        <span class="label">选择项：</span>
        <span class="value">{{ selectedOption || '暂无' }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import Head from './Head.vue'

// 响应式数据
const searchText = ref('')
const selectedValue = ref('')

// 下拉框选项
const dropdownOptions = ref([
  { text: '全部类型', value: 'all' },
  { text: '上市公司', value: 'listed' },
  { text: '高新技术企业', value: 'hightech' },
  { text: '专精特新', value: 'specialized' },
  { text: '独角兽企业', value: 'unicorn' },
])

// 计算选中项的显示文本
const selectedOption = computed(() => {
  const option = dropdownOptions.value.find(item => item.value === selectedValue.value)
  return option ? option.text : ''
})

// 事件处理函数
const handleSearch = value => {
  console.log('搜索:', value)
}

const handleClear = () => {
  console.log('清空搜索')
}
</script>

<style lang="scss" scoped>
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

.example-page {
  @apply min-h-screen bg-grey;
}

.status-display {
  @apply p-20 m-20 bg-white rounded-8;

  .status-item {
    @apply flex items-center mb-16;

    &:last-child {
      @apply mb-0;
    }

    .label {
      @apply text-28 mr-12 min-w-120;
      color: #666;
    }

    .value {
      @apply text-28 font-500;
      color: #333;
    }
  }
}
</style>
