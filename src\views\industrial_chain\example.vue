<template>
  <div class="example-page">
    <Head
      :search-placeholder="'请输入企业名称'"
      :dropdown-title="'选择类型'"
      :dropdown-options="dropdownOptions"
      @search="handleSearch"
      @clear="handleClear"
      @input="handleInput"
      @dropdown-change="handleDropdownChange"
    />
    
    <!-- 显示当前状态 -->
    <div class="status-display">
      <div class="status-item">
        <span class="label">搜索内容：</span>
        <span class="value">{{ searchText || '暂无' }}</span>
      </div>
      <div class="status-item">
        <span class="label">选择项：</span>
        <span class="value">{{ selectedOption || '暂无' }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import Head from './Head.vue'

// 响应式数据
const searchText = ref('')
const selectedOption = ref('')

// 下拉框选项
const dropdownOptions = ref([
  { text: '全部类型', value: 'all' },
  { text: '上市公司', value: 'listed' },
  { text: '高新技术企业', value: 'hightech' },
  { text: '专精特新', value: 'specialized' },
  { text: '独角兽企业', value: 'unicorn' }
])

// 事件处理函数
const handleSearch = (value) => {
  console.log('搜索:', value)
  searchText.value = value
}

const handleClear = () => {
  console.log('清空搜索')
  searchText.value = ''
}

const handleInput = (value) => {
  console.log('输入:', value)
  searchText.value = value
}

const handleDropdownChange = (value) => {
  console.log('下拉框选择:', value)
  const option = dropdownOptions.value.find(item => item.value === value)
  selectedOption.value = option ? option.text : ''
}
</script>

<style lang="scss" scoped>
.example-page {
  min-height: 100vh;
  background: #f7f7f7;
}

.status-display {
  padding: 20px;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  
  .status-item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .label {
      font-size: 28px;
      color: #666;
      margin-right: 12px;
      min-width: 120px;
    }
    
    .value {
      font-size: 28px;
      color: #333;
      font-weight: 500;
    }
  }
}
</style>
