<template>
  <div>
    <!-- 导航部分 -->
    <div v-if="isShowNav" class="app-header">
      <span class="back-icon" @click="router.back"></span> <span class="title">产业链招商</span>
    </div>
    <!-- 搜索框加下拉框 -->
  </div>
</template>

<script setup>
const route = useRoute()
const router = useRouter()
const isShowNav = computed(() => {
  return route.meta.isNavBar
})
</script>

<style lang="scss" scoped>
.app-header {
  position: relative;
  background-color: #fff;
  border-bottom: 1px solid #eee;
  height: 88px;
  padding: 0 46px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  .back-icon {
    position: absolute;
    z-index: 9;
    left: 34px;
    top: 50%;
    transform: translateY(-50%);
    width: 48px;
    height: 48px;
    &:before {
      display: block;
      position: absolute;
      left: 50%;
      top: 50%;
      transform-origin: 0 0;
      transform: rotate(-45deg) translate(-50%, -50%);
      content: '';
      width: 22px;
      height: 22px;
      background-color: transparent;
      border-color: #010101;
      border-style: solid;
      border-width: 4px 0 0 4px;
    }
  }
  .title {
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 34px;
    color: #010101;
    line-height: 1;
  }
}
</style>
