<template>
  <div class="head-container">
    <!-- 搜索框加下拉框 -->
    <div class="search-dropdown-wrapper">
      <!-- 左边搜索框 -->
      <div class="search-container">
        <van-field
          v-model="searchValue"
          clearable
          clear-trigger="always"
          enterkeyhint="search"
          :placeholder="searchPlaceholder"
          @clear="handleClear"
          @keyup.enter="handleSearch"
          @input="handleInput"
        >
          <template #left-icon>
            <div class="search-icon-wrapper">
              <svg-icon name="Search" class="search-icon" />
            </div>
          </template>
        </van-field>
      </div>

      <!-- 右边下拉框 -->
      <div class="dropdown-container">
        <van-dropdown-menu active-color="#2A72FF">
          <van-dropdown-item
            v-model="selectedValue"
            :options="dropdownOptions"
            :title="selectedValue || dropdownTitle"
            @change="handleDropdownChange"
          />
        </van-dropdown-menu>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineEmits, defineProps } from 'vue'

// Props
const props = defineProps({
  searchPlaceholder: {
    type: String,
    default: '请输入搜索内容',
  },
  dropdownTitle: {
    type: String,
    default: '请选择',
  },
  dropdownOptions: {
    type: Array,
    default: () => [
      { text: '选项1', value: 'option1' },
      { text: '选项2', value: 'option2' },
      { text: '选项3', value: 'option3' },
    ],
  },
})

// Emits
const emits = defineEmits(['search', 'clear', 'input', 'dropdown-change'])

// Reactive data
const searchValue = ref('')
const selectedValue = ref('')

// Methods
const handleClear = () => {
  searchValue.value = ''
  emits('clear')
}

const handleSearch = () => {
  emits('search', searchValue.value)
}

const handleInput = value => {
  emits('input', value)
}

const handleDropdownChange = value => {
  emits('dropdown-change', value)
}
</script>

<style lang="scss" scoped>
.head-container {
  padding: 20px;
  background: #fff;
}

.search-dropdown-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
  height: 72px;
}

.search-container {
  flex: 1;

  :deep(.van-field) {
    display: flex;
    align-items: center;
    border-radius: 8px;
    background: #f7f7f7;
    font-size: 28px;
    font-family: 'PingFang SC', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-weight: 400;
    outline: none;
    color: #282d30;
    caret-color: rgba(7, 110, 228, 1) !important;
    padding: 16px 0 16px 28px !important;
    height: 72px;

    .van-field__body {
      display: flex;
      align-items: center;
      height: 40px;
      color: #9b9eac;
      padding-right: 10px;

      input {
        padding-left: 16px;
        color: inherit;

        &::placeholder {
          font-size: 28px;
          color: #999;
        }
      }

      .van-icon-clear {
        z-index: 333;
      }
    }
  }
}

.search-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-icon {
  width: 36px;
  height: 36px;
}

.dropdown-container {
  width: 160px;
  height: 72px;

  :deep(.van-dropdown-menu) {
    height: 100%;

    .van-dropdown-menu__bar {
      height: 100%;
      box-shadow: none;
      background: #f7f7f7;
      border-radius: 8px;
      padding: 0 16px;
    }

    .van-dropdown-menu__item {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      .van-dropdown-menu__title {
        font-size: 28px;
        color: #282d30;
        font-weight: 400;
      }
    }
  }
}

// 激活状态样式
:deep(.activeMenuItem) {
  color: #2a72ff !important;
}
</style>
