<template>
  <div class="head-container">
    <div class="zw_bg"></div>
    <!-- 搜索框加下拉框 -->
    <div class="search-dropdown-wrapper">
      <!-- 左边搜索框 -->
      <div class="search-container">
        <van-field
          v-model="searchValue"
          clearable
          clear-trigger="always"
          enterkeyhint="search"
          placeholder="请输入产业链关键字"
          @clear="handleClear"
          @keyup.enter="handleSearch"
        >
          <template #left-icon>
            <div class="search-icon-wrapper">
              <svg-icon name="Search" class="search-icon" />
            </div>
          </template>
        </van-field>
      </div>

      <!-- 右边下拉框 -->
      <div class="dropdown-container" ref="dropdownRef">
        <div class="dropdown-trigger" @click="toggleDropdown">
          <span class="dropdown-text">{{ selectedText || dropdownTitle }}</span>
          <svg-icon name="arrow-down" class="dropdown-arrow" :class="{ 'rotate-180': isDropdownOpen }" />
        </div>

        <!-- 下拉面板 -->
        <div v-show="isDropdownOpen" class="dropdown-panel">
          <div
            v-for="option in dropdownOptions"
            :key="option.value"
            class="dropdown-option"
            :class="{ active: selectedValue === option.value }"
            @click="selectOption(option)"
          >
            {{ option.text }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineEmits, defineProps, ref, computed } from 'vue'
import { useVModel } from '@vueuse/core'
import { onClickOutside } from '@vueuse/core'

// Props
const props = defineProps({
  searchValue: {
    type: String,
    default: '',
  },
  selectedValue: {
    type: String,
    default: '',
  },
  searchPlaceholder: {
    type: String,
    default: '请输入搜索内容',
  },
  dropdownTitle: {
    type: String,
    default: '请选择',
  },
  dropdownOptions: {
    type: Array,
    default: () => [
      { text: '选项1', value: 'option1' },
      { text: '选项2', value: 'option2' },
      { text: '选项3', value: 'option3' },
    ],
  },
})

// Emits
const emits = defineEmits(['update:searchValue', 'update:selectedValue', 'search', 'clear'])

// 使用 useVModel 处理双向绑定
const searchValue = useVModel(props, 'searchValue', emits)
const selectedValue = useVModel(props, 'selectedValue', emits)

// 下拉框相关状态
const isDropdownOpen = ref(false)
const dropdownRef = ref(null)

// 计算选中项的显示文本
const selectedText = computed(() => {
  const option = props.dropdownOptions.find(item => item.value === selectedValue.value)
  return option ? option.text : ''
})

// 点击外部关闭下拉框
onClickOutside(dropdownRef, () => {
  isDropdownOpen.value = false
})

// Methods
const handleClear = () => {
  searchValue.value = ''
  emits('clear')
}

const handleSearch = () => {
  emits('search', searchValue.value)
}

// 下拉框方法
const toggleDropdown = () => {
  isDropdownOpen.value = !isDropdownOpen.value
}

const selectOption = option => {
  selectedValue.value = option.value
  isDropdownOpen.value = false
}
</script>

<style lang="scss" scoped>
.head-container {
  @apply p-24  w-full h-[156px] flex items-center
   bg-[url('@/assets/image/cyl/cyl_bg.png')] bg-no-repeat
   bg-cover relative;
  .zw_bg {
    @apply absolute w-full h-1 bg-[#07a6f0] top-0;
  }
}

.search-dropdown-wrapper {
  @apply flex items-center gap-32 h-72 w-full;
}

.search-container {
  @apply flex-1;

  :deep(.van-field) {
    @apply flex items-center rounded-8 bg-white  font-400 outline-none h-72;
    font-size: 28px;
    color: #282d30;
    caret-color: rgba(7, 110, 228, 1) !important;
    padding: 18px 24px !important;

    .van-field__body {
      @apply flex items-center h-40 pr-10;
      color: #9b9eac;

      input {
        @apply pl-16;
        color: inherit;

        &::placeholder {
          font-size: 28px;
          color: #999;
        }
      }

      .van-icon-clear {
        @apply z-[333];
      }
    }
  }
}

.search-icon-wrapper {
  @apply f-all-center;
}

.search-icon {
  @apply w-36 h-36;
}

.dropdown-container {
  @apply w-160 h-72;

  :deep(.van-dropdown-menu) {
    @apply h-full;

    .van-dropdown-menu__bar {
      @apply h-full  rounded-8 px-16;
      box-shadow: none;
    }

    .van-dropdown-menu__item {
      @apply h-full f-all-center;

      .van-dropdown-menu__title {
        @apply text-28 font-400;
        color: #282d30;
      }
    }
  }
}

// 激活状态样式
:deep(.activeMenuItem) {
  @apply text-selected;
}
</style>
