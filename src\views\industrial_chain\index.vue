<template>
  <div class="h-full flex flex-col overflow-x-hidden">
    <!-- 头部 -->
    <div class="flex-shrink-0"><Head /></div>
    <!-- tab部分 -->
    <div class="tab">
      <!-- 全部产业链 -->
      <div class="title">
        <div>全部产业链</div>
        <div>共<u>0</u>条产业链</div>
      </div>
      <!-- 卡片 -->
      <div>
        <Card />
        <Card />
      </div>
    </div>
  </div>
</template>

<script setup name="IndustrialChain">
import Head from './Head.vue'
import Card from './Card.vue'
</script>

<style lang="scss" scoped>
.tab {
  @apply flex-1 overflow-y-scroll bg-[#F7F7F7];
  -ms-overflow-style: none; /* IE 10+ */
  &::-webkit-scrollbar {
    width: 0;
    height: 0;
  }
  .title {
    @apply w-full f-y-center h-108 bg-white rounded-8 border-b border-solid border-[#EBEBEB];
    div:first-child {
      font-weight: 600;
      font-size: 32px;
      color: #07a6f0;
      margin: 0 24px;
    }
    div:last-child {
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 24px;
      color: #b2b2b2;
      u {
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 24px;
        color: #07a6f0;
        margin: 0 4px;
      }
    }
  }
}
</style>
