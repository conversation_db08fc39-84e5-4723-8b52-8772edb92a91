<template>
  <div class="card">
    <div class="title">{{ title }}</div>
    <div   class="box_wrap">
      <div class="box red" v-for="item in list" :key="item.chain_code">
        <div class="box_cont">
          <img src="" alt="11"></img>
          <div>{{ item.name }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  title: {
    type: String,
    default: '大健康产业',
  },
  source: {
    type: Array,
    default: () => [
      {
        belong_to: '',
        bespoke: '',
        chain_code: 'A1A1',
        children: [],
        fig: '0',
        level: '2',
        name: '生物医药产业',
        parent_code: 'A1',
        tag: '1',
      },
      {
        belong_to: '',
        bespoke: '',
        chain_code: 'A1A1',
        children: [],
        fig: '0',
        level: '2',
        name: '生物医药产业',
        parent_code: 'A1',
        tag: '1',
      },
      {
        belong_to: '',
        bespoke: '',
        chain_code: 'A1A111',
        children: [],
        fig: '0',
        level: '2',
        name: '生物医药产业',
        parent_code: 'A1',
        tag: '1',
      },
      {
        belong_to: '',
        bespoke: '',
        chain_code: 'A1A1111',
        children: [],
        fig: '0',
        level: '2',
        name: '生物医药产业',
        parent_code: 'A1',
        tag: '1',
      },
      {
        belong_to: '',
        bespoke: '',
        chain_code: 'A1A1111',
        children: [],
        fig: '0',
        level: '2',
        name: '生物医药产业',
        parent_code: 'A1',
        tag: '1',
      },
    ],
  },
})

const list = computed(() => {
  return props.source
})
</script>

<style lang="scss" scoped>
.card {
  @apply mb-20 bg-white;
  padding: 24px;
  .title {
    font-weight: 600;
    font-size: 32px;
    color: #404040;
    padding : 0 24px 24px;
  }
  .box_wrap {
    @apply flex flex-wrap;
    gap: 22px;
    .box {
      width: 340px;
      height: 120px;
      border-radius: 8px;
      overflow: hidden;
      padding: 24px 20px;
      .box_cont {
        display: flex;
        align-items: flex-start;
        
        img {
          width: 32px;
          height: 32px;
          margin-right: 12px;
        }
        div {
          width: 208px;
          height: 80px;
          font-family: PingFang SC, PingFang SC;
          font-weight: 600;
          font-size: 28px;
          color: #404040;
          
        }
      }  
    }
  }
}
</style>
