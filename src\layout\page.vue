<template>
  <div class="app-container">
    <div v-if="showNavBar" :class="['app-header', { dingzhi: isDingzhi }]">
      <span class="back-icon" @click="goBack"></span>
      <span class="title">{{ pageTitle }}</span>
    </div>
    <div ref="mainRef" class="main-container w-full">
      <router-view v-slot="{ Component }" v-wechat-title="$route.meta?.title">
        <keep-alive :include="['Home', ...include]" :exclude="excludes">
          <Component :is="Component" :key="$route.name" />
        </keep-alive>
      </router-view>
    </div>
    <Loading v-if="systemStore.loading" />
  </div>
</template>

<script setup>
import { excludes } from '@/router/keep'
import Loading from '@/components/Loading/index.vue'
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useSystemStore } from '@/store/modules/system'
import { backHandler } from '@/utils'
import { routes } from '@/router'

const route = useRoute()
const router = useRouter()
const systemStore = useSystemStore()
const mainRef = ref(null)
const include = getAliveKeys(routes)
const showNavBar = computed(() => route.meta.isNavBar)
const isDingzhi = computed(() => route.meta.title === '产业链招商')
const pageTitle = computed(() => {
  return route.meta?.title || '智慧招商'
})

const goBack = () => {
  if (route.name === 'Home') {
    return
  } else {
    backHandler()
  }
}

function getAliveKeys(routeList) {
  const result = []
  routeList.forEach(item => {
    if (item.meta?.keepAlive) {
      result.push(item.name)
    }
    if (item?.children?.length) {
      result.push(...getAliveKeys(item.children))
    }
  })
  return result
}

function onClickLeft() {
  backHandler()
}
onMounted(() => {
  nextTick(() => {
    const { height: h } = useWindowSize()
    const { top } = useElementBounding(mainRef, { immediate: true, windowResize: true }) //第一次拿不到
    mainRef.value.style.height = h.value - top.value - 3 + 'px'
  })
})
</script>

<style lang="scss" scoped>
:deep(.van-icon-arrow-left) {
  color: #000000 !important;
  font-size: 48px !important;
}
:deep(.van-nav-bar__title) {
  font-size: 34px;
}
// 需要边框自己加
:deep([class*='van-hairline']:after) {
  position: absolute;
  box-sizing: border-box;
  content: ' ';
  pointer-events: none;
  top: -50%;
  right: -50%;
  bottom: -50%;
  left: -50%;
  border: none;
  transform: scale(0.5);
}
.main-container {
  overflow: auto;
  -ms-overflow-style: none; /* IE 10+ */
  &::-webkit-scrollbar {
    width: 0;
    height: 0;
  }
}
.app-header {
  position: relative;
  background-color: #fff;
  border-bottom: 1px solid #eee;
  height: 88px;
  padding: 0 46px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;

  .back-icon {
    position: absolute;
    z-index: 9;
    left: 34px;
    top: 50%;
    transform: translateY(-50%);
    width: 48px;
    height: 48px;
    &:before {
      display: block;
      position: absolute;
      left: 50%;
      top: 50%;
      transform-origin: 0 0;
      transform: rotate(-45deg) translate(-50%, -50%);
      content: '';
      width: 22px;
      height: 22px;
      background-color: transparent;
      border-color: #010101;
      border-style: solid;
      border-width: 4px 0 0 4px;
    }
  }

  .title {
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 34px;
    color: #010101;
    line-height: 1;
  }
  &.dingzhi {
    background: #07a6f0 !important;
    .title {
      color: #fff !important;
    }
    .back-icon:before {
      border-color: #fff !important;
    }
  }
}
</style>
